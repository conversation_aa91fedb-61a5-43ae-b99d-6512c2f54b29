"""
Time series processing module for AEP_Julia

Handles loading and processing of wind time series data, converting to wind rose
format for AEP calculations.
"""
module TimeSeries

using CSV
using DataFrames
using Dates
using Statistics
using Printf

export load_timeseries, ts_to_freq_df, process_wind_rose

"""
    load_timeseries(filepath::String) -> DataFrame

Load time series data from file, handling multiple timestamp formats.

Expected format: space-separated file with columns:
- time: Timestamp (various formats supported)
- wd: Wind direction (degrees)
- ws: Wind speed (m/s)

Or alternatively:
- date: Date
- time: Time
- wd: Wind direction
- ws: Wind speed
"""
function load_timeseries(filepath::String)
    @assert isfile(filepath) "Time series file not found: $filepath"
    
    # Read the file - handle space-separated format
    ts = CSV.read(filepath, DataFrame, delim=' ')
    
    @info "Initial time series columns: $(names(ts))"
    @info "Time series shape: $(size(ts))"
    
    # Handle different timestamp formats (matching Python logic)
    if "date" in names(ts) && "time" in names(ts)
        # Format: separate date and time columns
        ts.timestamp = DateTime.(string.(ts.date) .* " " .* string.(ts.time))
        select!(ts, Not([:date, :time]))
        ts = ts[:, [:timestamp, :wd, :ws]]
        
    elseif "time" in names(ts) && !hasproperty(ts, :timestamp)
        # Format: index as date, time column exists
        if ts.time isa Vector && eltype(ts.time) <: AbstractString
            # Try to parse as datetime
            try
                ts.timestamp = DateTime.(ts.time)
                select!(ts, Not(:time))
                ts = ts[:, [:timestamp, :wd, :ws]]
            catch
                error("Unable to parse time column as datetime")
            end
        end
        
    elseif !("timestamp" in names(ts))
        # Try to infer timestamp from index or first column
        if names(ts)[1] in ["time", "datetime", "timestamp"]
            rename!(ts, names(ts)[1] => :timestamp)
        else
            error("Unable to determine timestamp format in timeseries file")
        end
    end
    
    # Ensure we have the required columns
    required_cols = ["timestamp", "wd", "ws"]
    missing_cols = setdiff(required_cols, names(ts))
    if !isempty(missing_cols)
        error("Missing required columns after processing: $missing_cols")
    end
    
    # Final selection to ensure column order
    ts = ts[:, [:timestamp, :wd, :ws]]
    
    @info "Processed time series: $(nrow(ts)) records"
    @info "Time range: $(minimum(ts.timestamp)) to $(maximum(ts.timestamp))"
    @info "Wind speed range: $(minimum(ts.ws)) to $(maximum(ts.ws)) m/s"
    @info "Wind direction range: $(minimum(ts.wd)) to $(maximum(ts.wd)) degrees"
    
    return ts
end

"""
    ts_to_freq_df(ts::DataFrame; ws_max::Float64=30.0) -> DataFrame

Convert time series to wind rose frequency distribution.

Parameters:
- ts: Time series DataFrame with columns [timestamp, wd, ws]
- ws_max: Maximum wind speed for binning

Returns DataFrame with columns:
- wd: Wind direction bin center
- ws: Wind speed bin center  
- freq: Frequency of occurrence (normalized to sum to 1)
"""
function ts_to_freq_df(ts::DataFrame; ws_max::Float64=30.0)
    # Define bins for wind direction and wind speed
    wd_bins = 0:10:350  # 10-degree bins
    ws_bins = 0:1:ceil(Int, ws_max)  # 1 m/s bins
    
    # Initialize frequency matrix
    freq_matrix = zeros(length(wd_bins), length(ws_bins))
    
    # Bin the data
    for row in eachrow(ts)
        # Find wind direction bin
        wd_idx = argmin(abs.(wd_bins .- row.wd)) 
        
        # Find wind speed bin  
        ws_idx = argmin(abs.(ws_bins .- row.ws))
        ws_idx = min(ws_idx, length(ws_bins))  # Cap at maximum bin
        
        freq_matrix[wd_idx, ws_idx] += 1
    end
    
    # Normalize frequencies
    freq_matrix ./= sum(freq_matrix)
    
    # Convert to DataFrame format
    result = DataFrame()
    for (wd_idx, wd) in enumerate(wd_bins)
        for (ws_idx, ws) in enumerate(ws_bins)
            if freq_matrix[wd_idx, ws_idx] > 1e-10  # Only include non-zero frequencies
                push!(result, (wd=Float64(wd), ws=Float64(ws), freq=freq_matrix[wd_idx, ws_idx]))
            end
        end
    end
    
    @info "Created wind rose with $(nrow(result)) wind condition bins"
    @info "Frequency sum: $(sum(result.freq))"
    
    return result
end

"""
    process_wind_directions(WR::DataFrame) -> Tuple{Vector{Float64}, Vector{Float64}}

Process wind rose data to create sorted arrays for FLORIS calculations.
Handles the edge case adjustment for minimum wind direction.
"""
function process_wind_directions(WR::DataFrame)
    # Get unique wind directions and speeds
    all_wd = sort(unique(WR.wd))
    all_ws = sort(unique(WR.ws))
    
    # Handle edge case: adjust minimum wind direction if needed (Python compatibility)
    if length(all_wd) > 1
        wd_diff = mode(diff(all_wd))
        if minimum(all_wd) == 0.0
            # Adjust 0° to negative value to maintain proper binning
            WR = copy(WR)
            WR.wd[WR.wd .== minimum(all_wd)] .= all_wd[2] - wd_diff
            all_wd = sort(unique(WR.wd))
        end
    end
    
    wd_array = Vector{Float64}(all_wd)
    ws_array = Vector{Float64}(all_ws)
    
    @info "Wind direction array: $(length(wd_array)) bins from $(minimum(wd_array)) to $(maximum(wd_array))"
    @info "Wind speed array: $(length(ws_array)) bins from $(minimum(ws_array)) to $(maximum(ws_array))"
    
    return wd_array, ws_array
end

"""
    load_and_process_timeseries(inputs_loc::String) -> Tuple{DataFrame, DataFrame, Vector{Float64}, Vector{Float64}}

Load timeseries and convert to wind rose, returning all needed data structures.
This function replicates the Python `load_and_process_timeseries` function.

Returns:
- ts: Original time series DataFrame
- WR: Wind rose frequency DataFrame
- wd_array: Sorted wind direction array
- ws_array: Sorted wind speed array
"""
function load_and_process_timeseries(inputs_loc::String)
    @info "Loading time series data..."
    start_time = time()
    
    # Load time series
    ts_file = joinpath(inputs_loc, "timeseries.txt")
    ts = load_timeseries(ts_file)
    
    # Convert to wind rose
    WR = ts_to_freq_df(ts)
    
    # Process wind arrays
    wd_array, ws_array = process_wind_directions(WR)
    
    elapsed = time() - start_time
    @info "Time series loading and WR conversion: $(round(elapsed, digits=2)) sec"
    @info "WR size: $(nrow(WR)), WD bins: $(length(wd_array)), WS bins: $(length(ws_array))"
    
    return ts, WR, wd_array, ws_array
end

"""
    calculate_sensitivity_factor(ts::DataFrame, FL_gross::Array, wd_array::Vector, ws_array::Vector, 
                                layout::DataFrame, keep_turbs::Vector, keep_turb_idxs::Vector) -> Float64

Calculate wind speed sensitivity factor by running 1% reduced wind speed case.
"""
function calculate_sensitivity_factor(ts::DataFrame, FL_gross::Array, wd_array::Vector, ws_array::Vector, 
                                     layout::DataFrame, keep_turbs::Vector, keep_turb_idxs::Vector)
    @info "Calculating wind speed sensitivity factor..."
    
    # Create sensitivity time series with 1% reduced wind speed
    ts_sens = copy(ts)
    ts_sens.ws .*= 0.99
    
    # Convert to frequency dataframe
    WR_sens = ts_to_freq_df(ts_sens, ws_max=maximum(ws_array) + 1)
    
    # Process powers for sensitivity case
    ordered_sens = []
    colnames = ["$(id)_MW" for id in layout.turb_ID]
    
    for row in eachrow(WR_sens)
        ws = row.ws
        wd = row.wd
        wd_idx = argmin(abs.(wd_array .- wd))
        ws_idx = argmin(abs.(ws_array .- ws))
        powers = FL_gross[wd_idx, ws_idx, :]
        push!(ordered_sens, powers)
    end
    
    unwaked_turbine_powers_df_sens = DataFrame(hcat(ordered_sens...), colnames)
    unwaked_turbine_powers_df_sens = unwaked_turbine_powers_df_sens[:, keep_turbs]
    
    # Calculate AEP for sensitivity case
    unwaked_final_results_sens = hcat(WR_sens, unwaked_turbine_powers_df_sens)
    unwaked_final_results_sens[!, "farm_power [MW]"] = sum(Matrix(unwaked_final_results_sens[:, keep_turbs]), dims=2)
    
    unwaked_turbine_aeps_sens = []
    for turb in keep_turbs
        aep = sum(unwaked_final_results_sens.freq .* unwaked_final_results_sens[:, turb]) * 365 * 24  # MWh
        push!(unwaked_turbine_aeps_sens, aep)
    end
    
    gross_AEP_sens = sum(unwaked_turbine_aeps_sens) / 1E3  # GWh
    gross_AEP = sum(unwaked_turbine_aeps_sens) / 1E3  # GWh (this should be computed from original case)
    
    # Calculate sensitivity factor
    sensitivity_factor = 100 * ((gross_AEP - gross_AEP_sens) / gross_AEP)
    
    @info "gross_AEP_sens: $gross_AEP_sens"
    @info "gross_AEP: $gross_AEP"
    @info "sensitivity_factor: $sensitivity_factor"
    
    # Save sensitivity factor to file
    open("sensitivity_factor.txt", "w") do f
        println(f, sensitivity_factor)
    end
    
    return sensitivity_factor
end

end # module TimeSeries