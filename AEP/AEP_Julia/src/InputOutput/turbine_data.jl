"""
Turbine data processing module for AEP_Julia

Handles conversion of CSV turbine performance data to YAML format
compatible with FLORIS.jl.
"""
module TurbineData

using CSV
using DataFrames
using YAML
using Printf

export prepare_turbine_yaml, sanitize_turbine_name, load_turbine_csv

"""
    sanitize_turbine_name(name::String) -> String

Sanitize turbine names by replacing invalid characters.
"""
function sanitize_turbine_name(name::String)
    return replace(lowercase(string(name)), r"[/:*?\"<>|]" => "_")
end

"""
    load_turbine_csv(filepath::String) -> DataFrame

Load turbine performance data from CSV file.

Expected CSV format:
- ws: Wind speed (m/s)
- cp: Power coefficient
- p: Power (kW)
- ct: Thrust coefficient
- hh: Hub height (m) - only needed for first row
- dia: Rotor diameter (m) - only needed for first row
- name: Turbine name - only needed for first row
"""
function load_turbine_csv(filepath::String)
    @assert isfile(filepath) "Turbine CSV file not found: $filepath"
    
    turb_df = CSV.read(filepath, DataFrame)
    
    # Validate required columns
    required_cols = ["ws", "cp", "p", "ct", "hh", "dia", "name"]
    missing_cols = setdiff(required_cols, names(turb_df))
    if !isempty(missing_cols)
        error("Missing required columns in turbine file: $missing_cols")
    end
    
    @info "Loaded turbine data: $(nrow(turb_df)) wind speed points"
    @info "Wind speed range: $(minimum(skipmissing(turb_df.ws))) to $(maximum(skipmissing(turb_df.ws))) m/s"
    
    return turb_df
end

"""
    add_boundary_conditions!(turb_df::DataFrame) -> DataFrame

Add boundary conditions at -10 and 60 m/s with zero power and thrust.
This ensures proper interpolation behavior in FLORIS.
"""
function add_boundary_conditions!(turb_df::DataFrame)
    # Create boundary condition rows
    boundary_rows = DataFrame(
        ws = [-10.0, 60.0],
        cp = [0.0, 0.0],
        p = [0.0, 0.0],
        ct = [0.0, 0.0],
        hh = [missing, missing],
        dia = [missing, missing],
        name = [missing, missing]
    )
    
    # Ensure consistent types
    for col in names(turb_df)
        if col in names(boundary_rows)
            # Convert to common type, allowing for missing values
            if eltype(turb_df[:, col]) <: Union{Missing, Number}
                boundary_rows[!, col] = convert(Vector{Union{Missing, Float64}}, boundary_rows[:, col])
            end
        end
    end
    
    # Append and sort by wind speed
    turb_df = vcat(turb_df, boundary_rows)
    sort!(turb_df, :ws)
    
    return turb_df
end

"""
    create_turbine_yaml_dict(turb_df::DataFrame) -> Dict

Create YAML dictionary structure from turbine DataFrame.
"""
function create_turbine_yaml_dict(turb_df::DataFrame)
    # Get metadata from first non-missing row
    first_row_idx = findfirst(row -> !ismissing(row.name), eachrow(turb_df))
    if first_row_idx === nothing
        error("No valid turbine metadata found in file")
    end
    
    first_row = turb_df[first_row_idx, :]
    safe_name = sanitize_turbine_name(string(first_row.name))
    
    # Create turbine dictionary matching FLORIS.jl format
    turbine_dict = Dict{String, Any}(
        "turbine_type" => safe_name,
        "generator_efficiency" => 1.0,
        "hub_height" => Float64(first_row.hh),
        "pP" => 1.88,  # Wake expansion parameter for power
        "pT" => 1.88,  # Wake expansion parameter for thrust
        "rotor_diameter" => Float64(first_row.dia),
        "TSR" => 8.0,  # Tip speed ratio
        "power_thrust_table" => Dict{String, Vector{Float64}}(
            "power" => Vector{Float64}(turb_df.cp),
            "thrust" => Vector{Float64}(turb_df.ct),
            "wind_speed" => Vector{Float64}(turb_df.ws)
        )
    )
    
    return turbine_dict
end

"""
    prepare_turbine_yaml(inputs_loc::String, turb_lib_path::String)

Process all turbine CSV files in inputs directory and create YAML files.
This function replicates the Python `prepare_turbine_yaml` function.

Parameters:
- inputs_loc: Directory containing turbine CSV files (files ending with 'turb.csv')
- turb_lib_path: Output directory for YAML files
"""
function prepare_turbine_yaml(inputs_loc::String, turb_lib_path::String)
    @info "Preparing turbine YAML files..."
    
    # Ensure output directory exists
    mkpath(turb_lib_path)
    
    # Find all turbine CSV files
    turb_files = filter(f -> endswith(f, "turb.csv"), readdir(inputs_loc))
    
    if isempty(turb_files)
        @warn "No turbine CSV files found in $inputs_loc"
        return
    end
    
    @info "Found $(length(turb_files)) turbine files: $turb_files"
    
    for file in turb_files
        filepath = joinpath(inputs_loc, file)
        @info "Processing turbine file: $file"
        
        try
            # Load turbine data
            turb_df = load_turbine_csv(filepath)
            
            # Add boundary conditions
            turb_df = add_boundary_conditions!(turb_df)
            
            # Create YAML dictionary
            turbine_dict = create_turbine_yaml_dict(turb_df)
            
            # Save YAML file
            safe_name = turbine_dict["turbine_type"]
            outfile = joinpath(turb_lib_path, "$(safe_name).yaml")
            
            YAML.write_file(outfile, turbine_dict)
            
            @info "Created YAML file: $outfile"
            @info "  Turbine: $(safe_name)"
            @info "  Hub height: $(turbine_dict["hub_height"]) m"
            @info "  Rotor diameter: $(turbine_dict["rotor_diameter"]) m"
            @info "  Wind speed points: $(length(turbine_dict["power_thrust_table"]["wind_speed"]))"
            
        catch e
            @error "Failed to process turbine file $file: $e"
            rethrow(e)
        end
    end
    
    @info "Turbine YAML preparation complete!"
end

"""
    validate_turbine_yaml(yaml_file::String) -> Bool

Validate that a turbine YAML file has the correct structure.
"""
function validate_turbine_yaml(yaml_file::String)
    try
        data = YAML.load_file(yaml_file)
        
        required_fields = ["turbine_type", "hub_height", "rotor_diameter", "power_thrust_table"]
        required_table_fields = ["power", "thrust", "wind_speed"]
        
        # Check top-level fields
        for field in required_fields
            if !haskey(data, field)
                @error "Missing required field: $field"
                return false
            end
        end
        
        # Check power_thrust_table structure
        table = data["power_thrust_table"]
        for field in required_table_fields
            if !haskey(table, field)
                @error "Missing required table field: $field"
                return false
            end
        end
        
        # Check array lengths are consistent
        lengths = [length(table[field]) for field in required_table_fields]
        if !all(l == lengths[1] for l in lengths)
            @error "Inconsistent array lengths in power_thrust_table"
            return false
        end
        
        @info "Turbine YAML validation passed: $yaml_file"
        return true
        
    catch e
        @error "Failed to validate turbine YAML $yaml_file: $e"
        return false
    end
end

end # module TurbineData