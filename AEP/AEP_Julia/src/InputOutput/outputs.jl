"""
Output generation module for AEP_Julia

Handles creation of all output files in the exact format expected by the Python implementation.
"""
module Outputs

using CSV
using DataFrames
using Plots
using Printf
using Statistics

export save_turbine_aep, save_directional_aep, save_production_timeseries
export create_directional_aep_plot, generate_summary_outputs

"""
    save_turbine_aep(aep_series::Vector, turbine_ids::Vector{String}, output_file::String)

Save individual turbine AEP results to CSV file.

Parameters:
- aep_series: Vector of AEP values in GWh
- turbine_ids: Vector of turbine IDs
- output_file: Output CSV file path

Output format matches Python: "turb_ID,AEP [GWh]"
"""
function save_turbine_aep(aep_series::Vector, turbine_ids::Vector{String}, output_file::String)
    # Create DataFrame
    df = DataFrame(
        "turb_ID" => turbine_ids,
        "AEP [GWh]" => aep_series
    )
    
    # Save to CSV
    CSV.write(output_file, df)
    
    total_aep = sum(aep_series)
    @info "Saved turbine AEP results to $output_file"
    @info "Total AEP: $(round(total_aep, digits=3)) GWh"
end

"""
    save_directional_aep(sectors::Vector, gross_aep::Vector, net_aep::Vector, 
                        wake_loss::Vector, output_file::String)

Save directional AEP breakdown to CSV file.

Output format matches Python with columns: Sector, Gross AEP, Net AEP, Wake Loss
"""
function save_directional_aep(sectors::Vector, gross_aep::Vector, net_aep::Vector, 
                             wake_loss::Vector, output_file::String)
    # Create DataFrame
    df = DataFrame(
        "Sector" => sectors,
        "Gross AEP" => gross_aep,
        "Net AEP" => net_aep,
        "Wake Loss" => wake_loss
    )
    
    # Set Sector as index (matching Python behavior)
    CSV.write(output_file, df)
    
    @info "Saved directional AEP results to $output_file"
end

"""
    save_production_timeseries(ts::DataFrame, layout::DataFrame, FL_net::Array, FL_gross::Array,
                              wd_array::Vector, ws_array::Vector, output_file::String)

Create and save production time series matching Python format.

Output columns: timestamp, wd, ws, net_power_MW, gross_power_MW
"""
function save_production_timeseries(ts::DataFrame, layout::DataFrame, FL_net::Array, FL_gross::Array,
                                   wd_array::Vector, ws_array::Vector, output_file::String)
    @info "Creating production time series..."
    
    # Initialize output DataFrame
    prod_ts = DataFrame(
        timestamp = ts.timestamp,
        wd = ts.wd,
        ws = ts.ws,
        net_power_MW = Float64[],
        gross_power_MW = Float64[]
    )
    
    # Get internal turbines only
    internal_mask = .!layout.external
    
    # Process each time step
    for (i, row) in enumerate(eachrow(ts))
        # Find closest wind conditions in arrays
        wd_idx = argmin(abs.(wd_array .- row.wd))
        ws_idx = argmin(abs.(ws_array .- row.ws))
        
        # Sum power for internal turbines only
        net_power = sum(FL_net[wd_idx, ws_idx, internal_mask])
        gross_power = sum(FL_gross[wd_idx, ws_idx, internal_mask])
        
        push!(prod_ts.net_power_MW, net_power)
        push!(prod_ts.gross_power_MW, gross_power)
    end
    
    # Round values for cleaner output (matching Python)
    prod_ts.wd = round.(prod_ts.wd, digits=1)
    prod_ts.ws = round.(prod_ts.ws, digits=2)
    prod_ts.net_power_MW = round.(prod_ts.net_power_MW, digits=3)
    prod_ts.gross_power_MW = round.(prod_ts.gross_power_MW, digits=3)
    
    # Save to CSV
    CSV.write(output_file, prod_ts, writeheader=true)
    
    @info "Saved production time series to $output_file"
    @info "Time series length: $(nrow(prod_ts)) hours"
end

"""
    create_directional_aep_plot(sectors::Vector, net_aep::Vector, wake_loss::Vector,
                               wake_loss_pct::Vector, title::String, output_file::String)

Create directional AEP bar plot matching Python visualization.
"""
function create_directional_aep_plot(sectors::Vector, net_aep::Vector, wake_loss::Vector,
                                    wake_loss_pct::Vector, title::String, output_file::String)
    @info "Creating directional AEP bar plot..."
    
    # Create the plot
    p = bar(sectors, net_aep, 
           width=20,
           label="Net AEP",
           color="#2071B5",
           xlabel="Sector (Degrees)",
           ylabel="AEP (GWh)",
           title=title)
    
    # Add wake loss bars on top
    bar!(p, sectors, wake_loss,
         bottom=net_aep,
         width=20,
         label="Wake Loss",
         color="#D1266B")
    
    # Add percentage labels on wake loss bars
    for (i, (sector, net, loss, pct)) in enumerate(zip(sectors, net_aep, wake_loss, wake_loss_pct))
        if loss > 0.01  # Only label significant losses
            annotate!(p, sector, net + loss + 0.1, text("$(round(pct, digits=2))%", 8, :center, "#D1266B"))
        end
    end
    
    # Style the plot
    plot!(p, legend=:topleft,
         grid=true,
         gridwidth=1,
         gridcolor=:lightgray,
         framestyle=:box)
    
    # Save the plot
    savefig(p, output_file)
    
    @info "Saved directional AEP plot to $output_file"
end

"""
    calculate_directional_aep(waked_results::DataFrame, unwaked_results::DataFrame) 
    -> Tuple{Vector, Vector, Vector, Vector, Vector}

Calculate directional AEP breakdown for 30-degree sectors.

Returns: (sectors, gross_aep, net_aep, wake_loss, wake_loss_pct)
"""
function calculate_directional_aep(waked_results::DataFrame, unwaked_results::DataFrame)
    @info "Calculating directional AEP breakdown..."
    
    # Add sector information to both datasets
    add_sector_column!(waked_results)
    add_sector_column!(unwaked_results)
    
    sectors = Float64[]
    gross_aep = Float64[]
    net_aep = Float64[]
    wake_loss = Float64[]
    wake_loss_pct = Float64[]
    
    # Calculate for each 30-degree sector
    for sector in 0:30:330
        push!(sectors, Float64(sector))
        
        # Gross AEP for this sector
        gross_sector = filter(row -> row.closest_sect == sector, unwaked_results)
        if nrow(gross_sector) > 0
            gross_energy = sum(gross_sector[!, "farm_power [MW]"] .* gross_sector.freq .* 8766 ./ 1E3)
        else
            gross_energy = 0.0
        end
        push!(gross_aep, gross_energy)
        
        # Net AEP for this sector
        net_sector = filter(row -> row.closest_sect == sector, waked_results)
        if nrow(net_sector) > 0
            net_energy = sum(net_sector[!, "farm_power [MW]"] .* net_sector.freq .* 8766 ./ 1E3)
        else
            net_energy = 0.0
        end
        push!(net_aep, net_energy)
        
        # Calculate wake loss
        loss = gross_energy - net_energy
        push!(wake_loss, loss)
        
        # Calculate percentage loss
        if gross_energy > 1e-6
            loss_pct = 100 * loss / gross_energy
            push!(wake_loss_pct, max(0.0, min(100.0, loss_pct)))
        else
            push!(wake_loss_pct, 0.0)
        end
    end
    
    return sectors, gross_aep, net_aep, wake_loss, wake_loss_pct
end

"""
    add_sector_column!(df::DataFrame)

Add 30-degree sector classification to wind rose DataFrame.
"""
function add_sector_column!(df::DataFrame)
    df[!, :closest_sect] = [custom_round(wd, 30) for wd in df.wd]
    
    # Handle 360° -> 0° mapping
    df.closest_sect[df.closest_sect .== 360] .= 0
end

"""
    custom_round(value::Float64, base::Int) -> Float64

Round value to nearest multiple of base (matching Python behavior).
"""
function custom_round(value::Float64, base::Int)
    return Float64(round(Int, value / base) * base)
end

"""
    generate_summary_outputs(waked_results::DataFrame, unwaked_results::DataFrame,
                            layout::DataFrame, keep_turb_idxs::Vector{Int},
                            outputs_loc::String, wake_model_name::String)

Generate all summary output files and plots.
"""
function generate_summary_outputs(waked_results::DataFrame, unwaked_results::DataFrame,
                                 layout::DataFrame, keep_turb_idxs::Vector{Int},
                                 outputs_loc::String, wake_model_name::String)
    @info "Generating summary outputs..."
    
    # Calculate directional AEP
    sectors, gross_aep, net_aep, wake_loss, wake_loss_pct = calculate_directional_aep(waked_results, unwaked_results)
    
    # Save directional AEP data
    save_directional_aep(sectors, gross_aep, net_aep, wake_loss, 
                        joinpath(outputs_loc, "Directional_AEP_df.csv"))
    
    # Create directional AEP plot
    plot_title = if contains(outputs_loc, "Output/")
        "External+Internal wake AEP and Wake Loss by Sector\\nWake Model: $wake_model_name"
    else
        "Internal Wake AEP and Wake Loss by Sector\\nWake Model: $wake_model_name"
    end
    
    create_directional_aep_plot(sectors, net_aep, wake_loss, wake_loss_pct, plot_title,
                               joinpath(outputs_loc, "Directional_AEP_bar_plot.png"))
    
    @info "Summary outputs generation complete!"
end

end # module Outputs