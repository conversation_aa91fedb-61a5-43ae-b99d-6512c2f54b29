"""
Layout processing module for AEP_Julia

Handles loading and processing of wind farm layout CSV files compatible with
the Python implementation.
"""
module Layouts

using CSV
using DataFrames
using Printf

export load_layout, process_layout, sanitize_turbine_name

"""
    sanitize_turbine_name(name::String) -> String

Sanitize turbine names by replacing invalid characters, maintaining
compatibility with Python implementation.
"""
function sanitize_turbine_name(name::String)
    return replace(lowercase(name), r"[/:*?\"<>|]" => "_")
end

"""
    load_layout(layout_file::String) -> DataFrame

Load and process a wind farm layout from CSV file.

Expected CSV format:
- turb_ID: Unique turbine identifier
- easting: X coordinate (UTM)
- northing: Y coordinate (UTM)  
- turb: Turbine type name
- external: Boolean flag (TRUE/FALSE) indicating external turbines

Returns processed DataFrame with:
- turb_ID: Original turbine ID
- x: X coordinate (renamed from easting)
- y: Y coordinate (renamed from northing)
- turb: Sanitized turbine type name
- external: Boolean flag
"""
function load_layout(layout_file::String)
    @assert isfile(layout_file) "Layout file not found: $layout_file"
    
    # Load CSV with proper type handling
    layout = CSV.read(layout_file, DataFrame)
    
    # Validate required columns
    required_cols = ["turb_ID", "easting", "northing", "turb", "external"]
    missing_cols = setdiff(required_cols, names(layout))
    if !isempty(missing_cols)
        error("Missing required columns in layout file: $missing_cols")
    end
    
    # Rename columns to match FLORIS.jl convention
    rename!(layout, :easting => :x, :northing => :y)
    
    # Process turbine names
    layout.turb = sanitize_turbine_name.(layout.turb)
    
    # Convert external column to boolean
    if eltype(layout.external) <: AbstractString
        layout.external = map(layout.external) do val
            uppercase(strip(val)) == "TRUE"
        end
    end
    
    @info "Loaded layout with $(nrow(layout)) turbines from $layout_file"
    @info "Internal turbines: $(sum(.!layout.external)), External turbines: $(sum(layout.external))"
    
    return layout
end

"""
    process_layout(layout_file::String) -> DataFrame

Wrapper function for load_layout to maintain compatibility with Python interface.
"""
process_layout(layout_file::String) = load_layout(layout_file)

"""
    check_external_turbines(layout_internal_file::String, layout_full_file::String) 
    -> Tuple{Bool, DataFrame, DataFrame}

Check if there are external turbines and load both layouts.
Returns (has_external, layout_internal, layout_full).
"""
function check_external_turbines(layout_internal_file::String, layout_full_file::String)
    # Load internal layout
    layout_internal = load_layout(layout_internal_file)
    
    # Load full layout  
    layout_full = load_layout(layout_full_file)
    
    # Check for external turbines
    has_external = any(layout_full.external)
    
    @info "Internal turbines: $(sum(.!layout_internal.external))"
    @info "Total turbines in full layout: $(nrow(layout_full))"
    @info "External turbines present: $has_external"
    
    return has_external, layout_internal, layout_full
end

"""
    get_internal_turbines(layout::DataFrame) -> Tuple{Vector{String}, Vector{Int}}

Get lists of internal turbine names and indices.
Returns (keep_turbs, keep_turb_idxs) where:
- keep_turbs: Turbine names with "_MW" suffix for power calculations
- keep_turb_idxs: Original row indices for internal turbines
"""
function get_internal_turbines(layout::DataFrame)
    internal_mask = .!layout.external
    keep_turb_idxs = findall(internal_mask)
    keep_turbs = ["$(layout.turb_ID[i])_MW" for i in keep_turb_idxs]
    
    return keep_turbs, keep_turb_idxs
end

end # module Layouts