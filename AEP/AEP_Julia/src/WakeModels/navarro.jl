"""
Navarro wake velocity deficit model for AEP_Julia

This module implements the custom Navarro wake model exactly as defined in the
Python implementation, ensuring numerical compatibility.
"""
module NavarroWakeModel

using LinearAlgebra
using Statistics

export NavarroVelocityDeficit, calculate_navarro_wake

"""
Navarro wake velocity deficit model parameters
"""
mutable struct NavarroVelocityDeficit
    wec_TI_multpl::Float64     # Turbulence intensity multiplier for wake expansion
    alpha::Float64              # Wake expansion exponent
    a::Float64                  # Wake expansion coefficient
    alpha2::Float64             # Velocity deficit decay exponent
    a2::Float64                 # Internal wake coefficient
    beta::Float64               # Velocity deficit exponent
    weCoeff::Float64            # Wake expansion turbulence coefficient
    alphaCoeff::Float64         # Far wake alpha coefficient
    farDistance::Float64        # Far wake distance threshold (diameters)
    lateralDistance::Float64    # Lateral distance threshold (diameters)
    gaussianProfile::Bool       # Use Gaussian profile (vs top-hat)
end

"""
    NavarroVelocityDeficit(; kwargs...)

Constructor for Navarro wake model with default parameters matching Python implementation.
"""
function NavarroVelocityDeficit(;
    wec_TI_multpl::Float64 = 0.082782006,   # From config file
    alpha::Float64 = 0.476,                 # From config file
    a::Float64 = 0.333,                     # From config file
    alpha2::Float64 = 0.56,                 # From config file
    a2::Float64 = 0.2,                      # From config file
    beta::Float64 = 2.0,                    # From config file
    weCoeff::Float64 = 0.5,                 # From config file
    alphaCoeff::Float64 = 0.0,              # From config file
    farDistance::Float64 = 21.0,            # From config file
    lateralDistance::Float64 = 3.0,         # From config file
    gaussianProfile::Bool = false           # From config file
)
    return NavarroVelocityDeficit(
        wec_TI_multpl, alpha, a, alpha2, a2, beta,
        weCoeff, alphaCoeff, farDistance, lateralDistance, gaussianProfile
    )
end

"""
    calculate_navarro_wake(model::NavarroVelocityDeficit, x::Array, y::Array, z::Array,
                          x_i::Float64, y_i::Float64, z_i::Float64,
                          axial_induction_i::Float64, deflection_field_i::Array,
                          turbulence_intensity_i::Float64, ambient_turbulence_intensity::Float64,
                          rotor_diameter_i::Float64, external_flag::Union{Nothing,Bool}=nothing,
                          turbine_idx::Union{Nothing,Int}=nothing) -> Array

Calculate Navarro wake velocity deficit.

This function replicates the Python implementation's logic exactly, including:
- Wake expansion calculations with turbulence dependency
- Far vs internal wake classification
- Gaussian vs top-hat profile options
- External turbine handling

Parameters:
- model: NavarroVelocityDeficit model parameters
- x, y, z: Grid coordinates
- x_i, y_i, z_i: Turbine position
- axial_induction_i: Turbine axial induction factor
- deflection_field_i: Wake deflection field
- turbulence_intensity_i: Local turbulence intensity
- ambient_turbulence_intensity: Ambient turbulence intensity
- rotor_diameter_i: Turbine rotor diameter
- external_flag: External turbine classification (optional)
- turbine_idx: Turbine index for external lookup (optional)

Returns:
- velocity_deficit: Wake velocity deficit array
"""
function calculate_navarro_wake(model::NavarroVelocityDeficit, 
                               x::Array, y::Array, z::Array,
                               x_i::Float64, y_i::Float64, z_i::Float64,
                               axial_induction_i::Float64, deflection_field_i::Array,
                               turbulence_intensity_i::Float64, ambient_turbulence_intensity::Float64,
                               rotor_diameter_i::Float64,
                               external_flag::Union{Nothing,Bool}=nothing,
                               turbine_idx::Union{Nothing,Int}=nothing)
    
    # Constants
    NUM_EPS = 1e-10
    rotor_radius = rotor_diameter_i / 2.0
    
    # Calculate distance arrays
    dx = x .- x_i
    dy = y .- y_i .- deflection_field_i
    dz = z .- z_i
    
    # Wake expansion parameters
    we = model.wec_TI_multpl * ambient_turbulence_intensity
    alpha = model.alpha
    alpha2 = model.alpha2
    a = model.a
    a2 = model.a2
    beta = model.beta
    weCoeff = model.weCoeff
    alphaCoeff = model.alphaCoeff
    farDistance = model.farDistance
    lateralDistance = model.lateralDistance
    gaussianProfile = model.gaussianProfile
    
    # Added turbulence intensity from wake effects
    deltaTI = turbulence_intensity_i - ambient_turbulence_intensity
    
    # Wake expansion parameters with turbulence dependency
    we_calc = we .+ weCoeff .* deltaTI
    
    # Determine if turbine is in far wake or internal wake regime
    if external_flag === nothing
        # Calculate distances in rotor diameters
        dx_D = dx ./ rotor_diameter_i
        dy_D = dy ./ rotor_diameter_i
        
        # For each wind direction, check if turbines fall within downstream/lateral bounds
        if ndims(dx_D) >= 2
            # Multi-dimensional case (multiple wind directions)
            near_f = (dx_D[:, 1, :, 1, 1] .<= farDistance) .& (dx_D[:, 1, :, 1, 1] .>= 0.01)
            lateral_f = abs.(dy_D[:, 1, :, 1, 1]) .<= lateralDistance
            comb = lateral_f .& near_f
            in_box = any(comb, dims=2)
            external = .!in_box
        else
            # Single turbine case
            external = [false]  # Default to internal
        end
    else
        external = external_flag
    end
    
    # Apply external classification to alpha modification
    if isa(external, Bool)
        external_array = fill(external, size(dx))
    else
        external_array = reshape(external, :, 1, 1, 1, 1)
    end
    
    alphaWT = alphaCoeff .* deltaTI .* external_array
    
    # Calculate velocity deficit based on profile type
    if gaussianProfile
        # Gaussian profile implementation
        boundary_line = 5.0 .* rotor_radius .* (we_calc .* (dx ./ rotor_radius) ./ a).^alpha .+ rotor_radius
        
        sigma_x2 = rotor_radius .* (we_calc .* (dx ./ rotor_radius) ./ a).^alpha .+ rotor_radius
        sigma = sigma_x2 ./ 2.0
        r = sqrt.(dy.^2 .+ dz.^2)
        D = rotor_radius * 2.0
        rD = r ./ D
        
        deficit = 1.0 ./ (1.0 .+ (we_calc .* (dx ./ rotor_radius) ./ a2).^(alpha2 .- alphaWT)).^beta
        deficitArea = (2.0 .* sigma_x2 ./ D) .* deficit
        
        pi_const = π
        e_const = ℯ
        C = deficitArea ./ ((sigma ./ D) .* sqrt(2.0 * pi_const))
        c = C .* exp.(-0.5 .* (rD.^2) ./ (sigma ./ D).^2)
        
    else
        # Top-hat profile implementation
        boundary_line = rotor_radius .* (we_calc .* (dx ./ rotor_radius) ./ a).^alpha .+ rotor_radius
        c = 1.0 ./ (1.0 .+ (we_calc .* (dx ./ rotor_radius) ./ a2).^(alpha2 .- alphaWT) .+ NUM_EPS).^beta
    end
    
    # Apply wake boundary conditions
    downstream_mask = dx .> 0.1  # Turbines must be downstream
    boundary_mask = sqrt.(dy.^2 .+ dz.^2) .< boundary_line  # Within wake boundary
    
    mask = downstream_mask .& boundary_mask
    c[.!mask] .= 0.0
    
    # Calculate final velocity deficit
    velocity_deficit = 2.0 .* axial_induction_i .* c
    
    return velocity_deficit
end

"""
    create_navarro_config(parameters::Dict) -> Dict

Create Navarro wake model configuration dictionary for FLORIS.jl integration.
"""
function create_navarro_config(parameters::Dict)
    return Dict(
        "velocity_model" => "navarro",
        "navarro" => parameters
    )
end

"""
    validate_navarro_parameters(params::Dict) -> Bool

Validate Navarro wake model parameters.
"""
function validate_navarro_parameters(params::Dict)
    required_params = [
        "alpha", "a", "beta", "wec_TI_multpl", "alpha2", "a2",
        "weCoeff", "alphaCoeff", "farDistance", "lateralDistance", "gaussianProfile"
    ]
    
    for param in required_params
        if !haskey(params, param)
            @error "Missing required Navarro parameter: $param"
            return false
        end
    end
    
    # Validate parameter ranges
    if params["alpha"] <= 0 || params["alpha"] > 2
        @warn "Alpha parameter outside typical range (0, 2]: $(params["alpha"])"
    end
    
    if params["beta"] <= 0
        @error "Beta parameter must be positive: $(params["beta"])"
        return false
    end
    
    @info "Navarro parameter validation passed"
    return true
end

end # module NavarroWakeModel