"""
InputOutput module - aggregates all input/output functionality
"""
module InputOutput

include("InputOutput/layouts.jl")
include("InputOutput/timeseries.jl")
include("InputOutput/turbine_data.jl")
include("InputOutput/outputs.jl")

using .Layouts
using .TimeSeries
using .TurbineData
using .Outputs

# Re-export key functions
export load_layout, process_layout, check_external_turbines, get_internal_turbines
export load_timeseries, ts_to_freq_df, load_and_process_timeseries, calculate_sensitivity_factor
export prepare_turbine_yaml, sanitize_turbine_name, load_turbine_csv
export save_turbine_aep, save_directional_aep, save_production_timeseries, generate_summary_outputs

end # module InputOutput