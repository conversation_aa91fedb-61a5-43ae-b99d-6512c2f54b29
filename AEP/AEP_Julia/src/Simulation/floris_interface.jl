"""
FLORIS interface module for AEP_Julia

Provides a Julia implementation of FlorisInterface that maintains API compatibility
with the Python version while integrating with FLORIS.jl.
"""
module FlorisInterfaceJulia

using YAML
using DataFrames
using LinearAlgebra
using Statistics
using Printf

# Import FLORIS.jl components
using ..Floris
using ..NavarroWakeModel
using ..Layouts
using ..TimeSeries

export FlorisInterfaceJulia, initialize_floris, calculate_wake, calculate_no_wake
export get_turbine_powers, reinitialize

"""
Julia equivalent of Python FlorisInterface class.
"""
mutable struct FlorisInterfaceJulia
    config_file::String
    floris_model::Union{FlorisModel, Nothing}
    layout_x::Vector{Float64}
    layout_y::Vector{Float64}
    turbine_types::Vector{String}
    wind_directions::Vector{Float64}
    wind_speeds::Vector{Float64}
    turbine_powers::Union{Array{Float64, 3}, Nothing}  # [wd, ws, turbine]
    reference_wind_height::Float64
    
    # Configuration parameters
    config::Dict{String, Any}
    
    function FlorisInterfaceJulia(config_file::String)
        @assert isfile(config_file) "Configuration file not found: $config_file"
        
        # Load configuration
        config = YAML.load_file(config_file)
        
        new(
            config_file,
            nothing,         # floris_model (initialized later)
            Float64[],       # layout_x
            Float64[],       # layout_y  
            String[],        # turbine_types
            Float64[],       # wind_directions
            Float64[],       # wind_speeds
            nothing,         # turbine_powers
            get(config["flow_field"], "reference_wind_height", 100.0),
            config
        )
    end
end

"""
    reinitialize(fi::FlorisInterfaceJulia; layout=nothing, turbine_type=nothing,
                wind_directions=nothing, wind_speeds=nothing)

Reinitialize FLORIS interface with new layout and/or wind conditions.
Maintains compatibility with Python FlorisInterface.reinitialize().
"""
function reinitialize(fi::FlorisInterfaceJulia; 
                     layout=nothing, 
                     turbine_type=nothing,
                     wind_directions=nothing, 
                     wind_speeds=nothing)
    
    @info "Reinitializing FLORIS interface..."
    start_time = time()
    
    # Update layout if provided
    if layout !== nothing
        if isa(layout, Tuple) && length(layout) == 2
            fi.layout_x = Vector{Float64}(layout[1])
            fi.layout_y = Vector{Float64}(layout[2])
        else
            error("Layout must be a tuple of (x_coordinates, y_coordinates)")
        end
    end
    
    # Update turbine types if provided
    if turbine_type !== nothing
        if isa(turbine_type, Vector)
            fi.turbine_types = turbine_type
        else
            # Single turbine type for all turbines
            fi.turbine_types = fill(turbine_type, length(fi.layout_x))
        end
    end
    
    # Update wind conditions if provided
    if wind_directions !== nothing
        fi.wind_directions = Vector{Float64}(wind_directions)
    end
    
    if wind_speeds !== nothing
        fi.wind_speeds = Vector{Float64}(wind_speeds)
    end
    
    # Validate inputs
    n_turbines = length(fi.layout_x)
    if length(fi.layout_y) != n_turbines
        error("Layout x and y coordinates must have same length")
    end
    
    if length(fi.turbine_types) != n_turbines
        error("Must specify turbine type for each turbine")
    end
    
    # Initialize FLORIS model
    fi.floris_model = create_floris_model(fi)
    
    elapsed = time() - start_time
    @info "FLORIS reinitialize runtime: $(round(elapsed, digits=2)) sec"
    @info "Layout: $(n_turbines) turbines"
    @info "Wind conditions: $(length(fi.wind_directions)) directions × $(length(fi.wind_speeds)) speeds"
    
    return fi
end

"""
    create_floris_model(fi::FlorisInterfaceJulia) -> FlorisModel

Create FLORIS.jl model from interface configuration.
"""
function create_floris_model(fi::FlorisInterfaceJulia)
    # Create wind farm layout
    farm = create_wind_farm(fi)
    
    # Create flow field
    flow_field = create_flow_field(fi)
    
    # Create wake model
    wake_model = create_wake_model(fi)
    
    # Create FLORIS model
    model = FlorisModel(
        configuration=fi.config,
        farm=farm,
        flow_field=flow_field,
        wake=wake_model
    )
    
    return model
end

"""
    create_wind_farm(fi::FlorisInterfaceJulia) -> Farm

Create wind farm from layout data.
"""
function create_wind_farm(fi::FlorisInterfaceJulia)
    # For now, create a simplified farm structure
    # This would need to be expanded based on FLORIS.jl's Farm structure
    
    turbine_positions = hcat(fi.layout_x, fi.layout_y)
    
    # Create turbine objects based on types
    turbines = []
    for (i, turb_type) in enumerate(fi.turbine_types)
        # Load turbine definition (simplified)
        turbine = create_turbine(turb_type, fi.layout_x[i], fi.layout_y[i])
        push!(turbines, turbine)
    end
    
    # This is a placeholder - actual implementation would use FLORIS.jl's Farm constructor
    return turbines
end

"""
    create_flow_field(fi::FlorisInterfaceJulia) -> FlowField

Create flow field from wind conditions.
"""
function create_flow_field(fi::FlorisInterfaceJulia)
    # Extract flow field parameters from config
    flow_config = fi.config["flow_field"]
    
    flow_field = Dict(
        "wind_directions" => fi.wind_directions,
        "wind_speeds" => fi.wind_speeds,
        "wind_shear" => get(flow_config, "wind_shear", 0.08),
        "wind_veer" => get(flow_config, "wind_veer", 0.0),
        "air_density" => get(flow_config, "air_density", 1.23),
        "turbulence_intensity" => get(flow_config, "turbulence_intensity", 0.045),
        "reference_wind_height" => get(flow_config, "reference_wind_height", 100.0)
    )
    
    return flow_field
end

"""
    create_wake_model(fi::FlorisInterfaceJulia) -> WakeModel

Create wake model from configuration.
"""
function create_wake_model(fi::FlorisInterfaceJulia)
    wake_config = fi.config["wake"]
    
    # Handle Navarro wake model specifically
    if wake_config["model_strings"]["velocity_model"] == "navarro"
        navarro_params = wake_config["wake_velocity_parameters"]["navarro"]
        wake_model = NavarroVelocityDeficit(;
            wec_TI_multpl = navarro_params["wec_TI_multpl"],
            alpha = navarro_params["alpha"],
            a = navarro_params["a"],
            alpha2 = navarro_params["alpha2"],
            a2 = navarro_params["a2"],
            beta = navarro_params["beta"],
            weCoeff = navarro_params["weCoeff"],
            alphaCoeff = navarro_params["alphaCoeff"],
            farDistance = navarro_params["farDistance"],
            lateralDistance = navarro_params["lateralDistance"],
            gaussianProfile = navarro_params["gaussianProfile"]
        )
        return wake_model
    else
        # Use default FLORIS.jl wake models
        return create_default_wake_model(wake_config)
    end
end

"""
    create_turbine(turb_type::String, x::Float64, y::Float64) -> Turbine

Create turbine object from type and position.
"""
function create_turbine(turb_type::String, x::Float64, y::Float64)
    # This is a placeholder - actual implementation would load turbine YAML files
    # and create proper FLORIS.jl Turbine objects
    
    return Dict(
        "turbine_type" => turb_type,
        "x" => x,
        "y" => y
    )
end

"""
    calculate_wake(fi::FlorisInterfaceJulia; logfile=nothing)

Calculate wake effects for all wind conditions.
"""
function calculate_wake(fi::FlorisInterfaceJulia; logfile=nothing)
    @assert fi.floris_model !== nothing "Must call reinitialize() first"
    
    @info "Calculating wake effects..."
    start_time = time()
    
    n_wd = length(fi.wind_directions)
    n_ws = length(fi.wind_speeds) 
    n_turbines = length(fi.layout_x)
    
    # Initialize power array
    fi.turbine_powers = zeros(n_wd, n_ws, n_turbines)
    
    # Calculate for each wind condition
    for (wd_idx, wd) in enumerate(fi.wind_directions)
        for (ws_idx, ws) in enumerate(fi.wind_speeds)
            # Calculate turbine powers for this condition
            powers = calculate_condition_powers(fi, wd, ws, with_wake=true)
            fi.turbine_powers[wd_idx, ws_idx, :] = powers
        end
    end
    
    elapsed = time() - start_time
    @info "Wake calculation complete: $(round(elapsed, digits=2)) seconds"
    
    if logfile !== nothing
        open(logfile, "a") do f
            println(f, "Wake calculation runtime: $(round(elapsed, digits=2)) sec")
        end
    end
    
    return fi.turbine_powers
end

"""
    calculate_no_wake(fi::FlorisInterfaceJulia)

Calculate power without wake effects (gross power).
"""
function calculate_no_wake(fi::FlorisInterfaceJulia)
    @assert fi.floris_model !== nothing "Must call reinitialize() first"
    
    @info "Calculating no-wake (gross) power..."
    start_time = time()
    
    n_wd = length(fi.wind_directions)
    n_ws = length(fi.wind_speeds)
    n_turbines = length(fi.layout_x)
    
    # Initialize power array
    fi.turbine_powers = zeros(n_wd, n_ws, n_turbines)
    
    # Calculate for each wind condition
    for (wd_idx, wd) in enumerate(fi.wind_directions)
        for (ws_idx, ws) in enumerate(fi.wind_speeds)
            # Calculate turbine powers without wake effects
            powers = calculate_condition_powers(fi, wd, ws, with_wake=false)
            fi.turbine_powers[wd_idx, ws_idx, :] = powers
        end
    end
    
    elapsed = time() - start_time
    @info "No-wake calculation complete: $(round(elapsed, digits=2)) seconds"
    
    return fi.turbine_powers
end

"""
    calculate_condition_powers(fi::FlorisInterfaceJulia, wd::Float64, ws::Float64; with_wake::Bool=true) -> Vector{Float64}

Calculate turbine powers for a single wind condition.
"""
function calculate_condition_powers(fi::FlorisInterfaceJulia, wd::Float64, ws::Float64; with_wake::Bool=true)
    n_turbines = length(fi.layout_x)
    powers = zeros(n_turbines)
    
    # For each turbine, calculate its power
    for i in 1:n_turbines
        if with_wake
            # Calculate effective wind speed considering wake effects
            effective_ws = calculate_effective_wind_speed(fi, i, wd, ws)
        else
            # No wake effects - use free stream wind speed
            effective_ws = ws
        end
        
        # Calculate power from effective wind speed
        powers[i] = calculate_turbine_power(fi, i, effective_ws)
    end
    
    return powers
end

"""
    calculate_effective_wind_speed(fi::FlorisInterfaceJulia, turbine_idx::Int, wd::Float64, ws::Float64) -> Float64

Calculate effective wind speed at turbine considering wake effects.
"""
function calculate_effective_wind_speed(fi::FlorisInterfaceJulia, turbine_idx::Int, wd::Float64, ws::Float64)
    # This is a simplified implementation
    # Real implementation would use the Navarro wake model
    
    # For now, return free stream wind speed (placeholder)
    return ws
end

"""
    calculate_turbine_power(fi::FlorisInterfaceJulia, turbine_idx::Int, wind_speed::Float64) -> Float64

Calculate power for a single turbine at given wind speed.
"""
function calculate_turbine_power(fi::FlorisInterfaceJulia, turbine_idx::Int, wind_speed::Float64)
    # This is a placeholder - real implementation would:
    # 1. Load turbine power curve from YAML file
    # 2. Interpolate power at given wind speed
    # 3. Apply any corrections (air density, etc.)
    
    # Simple placeholder power curve
    if wind_speed < 4.0
        return 0.0
    elseif wind_speed > 25.0
        return 0.0
    elseif wind_speed < 12.0
        # Ramp up to rated power
        return 5000.0 * (wind_speed - 4.0) / 8.0  # kW
    else
        # Rated power
        return 5000.0  # kW
    end
end

"""
    get_turbine_powers(fi::FlorisInterfaceJulia) -> Array{Float64, 3}

Get turbine powers array [wd, ws, turbine] in Watts.
"""
function get_turbine_powers(fi::FlorisInterfaceJulia)
    @assert fi.turbine_powers !== nothing "Must call calculate_wake() or calculate_no_wake() first"
    
    # Convert from kW to W to match Python interface
    return fi.turbine_powers .* 1000.0
end

"""
    assign_hub_height_to_ref_height(fi::FlorisInterfaceJulia)

Set reference wind height to turbine hub height(s).
"""
function assign_hub_height_to_ref_height(fi::FlorisInterfaceJulia)
    # This is a placeholder - real implementation would:
    # 1. Get hub heights from turbine definitions
    # 2. Use average if multiple turbine types
    # 3. Set as reference wind height
    
    fi.reference_wind_height = 100.0  # Default hub height
    @info "Set reference wind height to $(fi.reference_wind_height) m"
end

end # module FlorisInterfaceJulia