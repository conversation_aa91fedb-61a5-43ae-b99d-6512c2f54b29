"""
Shared computations module for AEP_Julia

Implements the SharedComputations class and related optimization functions
to eliminate redundant calculations between internal and full simulations.
"""
module SharedComputations

using DataFrames
using Printf

export SharedComputations, compute_gross_power_once, check_external_turbines

"""
Julia equivalent of Python SharedComputations class.
Holds shared computation results to avoid redundancy.
"""
mutable struct SharedComputations
    ts::Union{DataFrame, Nothing}
    WR::Union{DataFrame, Nothing}
    wd_array::Union{Vector{Float64}, Nothing}
    ws_array::Union{Vector{Float64}, Nothing}
    turbine_yamls_prepared::Bool
    sensitivity_factor::Union{Float64, Nothing}
    layout_internal::Union{DataFrame, Nothing}
    layout_full::Union{DataFrame, Nothing}
    has_external_turbines::Union{Bool, Nothing}
    gross_power_cache::Dict{String, Any}  # Cache gross power by layout key
    
    function SharedComputations()
        new(
            nothing,    # ts
            nothing,    # WR
            nothing,    # wd_array
            nothing,    # ws_array
            false,      # turbine_yamls_prepared
            nothing,    # sensitivity_factor
            nothing,    # layout_internal
            nothing,    # layout_full
            nothing,    # has_external_turbines
            Dict()      # gross_power_cache
        )
    end
end

"""
    compute_gross_power_once(fi, layout::DataFrame, wd_array::Vector, ws_array::Vector, 
                           WR::DataFrame; ref_ht=nothing, log_loc=nothing) -> Dict

Compute gross power (unwaked) once and return all needed data.
This function replicates the Python `compute_gross_power_once` function.

Returns Dictionary containing:
- FL_gross: Gross power array [wd, ws, turbine]
- unwaked_final_results: Wind rose with power data
- unwaked_turb_aeps_df: Individual turbine AEPs
- gross_AEP: Total gross AEP
- keep_turbs: List of internal turbine names with "_MW" suffix
- keep_turb_idxs: Indices of internal turbines
- ref_ht: Reference height used
"""
function compute_gross_power_once(fi, layout::DataFrame, wd_array::Vector, ws_array::Vector, 
                                 WR::DataFrame; ref_ht=nothing, log_loc=nothing)
    @info "Computing gross power (unwaked)..."
    start_time = time()
    
    # Initialize FLORIS with layout
    ref_ht = initialize_floris_with_layout(fi, layout, wd_array, ws_array, ref_ht, log_loc)
    
    # Calculate no-wake power
    calculate_no_wake(fi)
    FL_gross = get_turbine_powers(fi) ./ 1E6  # Convert to MW
    
    # Replace NaN values with zeros
    FL_gross[isnan.(FL_gross)] .= 0.0
    
    # Process results for internal turbines only
    internal_mask = .!layout.external
    keep_turb_idxs = findall(internal_mask)
    keep_turbs = ["$(layout.turb_ID[i])_MW" for i in keep_turb_idxs]
    
    @info "Internal turbines: $(length(keep_turbs))"
    @info "Total turbines: $(nrow(layout))"
    
    # Create gross power dataframe
    ordered_powers = []
    colnames = ["$(id)_MW" for id in layout.turb_ID]
    
    for row in eachrow(WR)
        ws = row.ws
        wd = row.wd
        wd_idx = argmin(abs.(wd_array .- wd))
        ws_idx = argmin(abs.(ws_array .- ws))
        powers = FL_gross[wd_idx, ws_idx, :]
        push!(ordered_powers, powers)
    end
    
    # Convert to DataFrame
    power_matrix = hcat(ordered_powers...)  # Stack as columns
    unwaked_turbine_powers_df = DataFrame(power_matrix', colnames)
    
    # Select only internal turbines
    unwaked_turbine_powers_df = unwaked_turbine_powers_df[:, keep_turbs]
    
    # Calculate gross AEP
    unwaked_final_results = hcat(WR, unwaked_turbine_powers_df)
    unwaked_final_results[!, "farm_power [MW]"] = sum(Matrix(unwaked_final_results[:, keep_turbs]), dims=2)
    
    # Calculate individual turbine AEPs
    unwaked_turbine_aeps = Float64[]
    for turb_col in keep_turbs
        aep = sum(unwaked_final_results.freq .* unwaked_final_results[:, turb_col]) * 8766 / 1E3  # GWh
        push!(unwaked_turbine_aeps, aep)
    end
    
    # Create AEP series indexed by turbine ID
    unwaked_turb_aeps_df = DataFrame(
        turb_ID = layout.turb_ID[keep_turb_idxs],
        aep = unwaked_turbine_aeps
    )
    
    gross_AEP = sum(unwaked_turbine_aeps)
    
    elapsed = time() - start_time
    @info "Gross power computation time: $(round(elapsed, digits=2)) sec"
    @info "Gross AEP: $(round(gross_AEP, digits=3)) GWh"
    
    return Dict(
        "FL_gross" => FL_gross,
        "unwaked_final_results" => unwaked_final_results,
        "unwaked_turb_aeps_df" => unwaked_turb_aeps_df,
        "gross_AEP" => gross_AEP,
        "keep_turbs" => keep_turbs,
        "keep_turb_idxs" => keep_turb_idxs,
        "ref_ht" => ref_ht
    )
end

"""
    initialize_floris_with_layout(fi, layout::DataFrame, wd_array::Vector, ws_array::Vector,
                                 ref_ht=nothing, log_loc=nothing) -> Float64

Initialize FLORIS interface with layout and wind conditions.
Returns the reference height used.
"""
function initialize_floris_with_layout(fi, layout::DataFrame, wd_array::Vector, ws_array::Vector,
                                      ref_ht=nothing, log_loc=nothing)
    start_time = time()
    
    # Reinitialize FLORIS
    reinitialize(fi,
                layout=(layout.x, layout.y),
                turbine_type=layout.turb,
                wind_directions=wd_array,
                wind_speeds=ws_array)
    
    elapsed = time() - start_time
    @info "FLORIS reinitialize runtime: $(round(elapsed, digits=2)) sec"
    
    # Determine reference height
    internal_turbs = unique(layout.turb[.!layout.external])
    
    if ref_ht === nothing
        if length(internal_turbs) > 1
            # Multiple turbine types - would need to get hub heights from YAML files
            # For now, use default
            ref_ht = 100.0  # Default hub height
            if log_loc !== nothing
                write_log("Multiple internal turbine types detected. Using default hub height as reference.", log_loc)
            end
        else
            # Single turbine type - would get hub height from YAML
            ref_ht = 100.0  # Default hub height
        end
    end
    
    # Set reference height (would update FLORIS model)
    # fi.floris_model.flow_field.reference_wind_height = ref_ht
    
    return ref_ht
end

"""
    write_log(message::String, log_file::String)

Write message to log file with timestamp.
"""
function write_log(message::String, log_file::String)
    timestamp = Dates.now()
    open(log_file, "a") do f
        println(f, "[$timestamp] $message")
    end
end

"""
    check_external_turbines(layout_internal_file::String, layout_full_file::String, inputs_loc::String)
    -> Tuple{Bool, DataFrame, DataFrame}

Check if there are external turbines and load layouts.
Returns (has_external, layout_internal, layout_full).
"""
function check_external_turbines(layout_internal_file::String, layout_full_file::String, inputs_loc::String)
    # Import layout loading functionality
    using ..Layouts
    
    # Load internal layout
    internal_path = joinpath(inputs_loc, layout_internal_file)
    layout_internal = load_layout(internal_path)
    
    # Load full layout
    full_path = joinpath(inputs_loc, layout_full_file)
    layout_full = load_layout(full_path)
    
    # Check for external turbines
    has_external = any(layout_full.external)
    
    @info "Internal turbines: $(sum(.!layout_internal.external))"
    @info "Total turbines in full layout: $(nrow(layout_full))"
    @info "External turbines present: $has_external"
    
    return has_external, layout_internal, layout_full
end

"""
    memory_footprint() -> Float64

Get current memory usage in MB (approximation).
"""
function memory_footprint()
    # Julia doesn't have a direct equivalent to Python's memory_profiler
    # This is a placeholder that returns 0
    return 0.0
end

"""
    cache_gross_power(shared::SharedComputations, layout_key::String, gross_data::Dict)

Cache gross power results for potential reuse.
"""
function cache_gross_power(shared::SharedComputations, layout_key::String, gross_data::Dict)
    shared.gross_power_cache[layout_key] = gross_data
    @info "Cached gross power data for layout: $layout_key"
end

"""
    get_cached_gross_power(shared::SharedComputations, layout_key::String) -> Union{Dict, Nothing}

Retrieve cached gross power data if available.
"""
function get_cached_gross_power(shared::SharedComputations, layout_key::String)
    if haskey(shared.gross_power_cache, layout_key)
        @info "Using cached gross power data for layout: $layout_key"
        return shared.gross_power_cache[layout_key]
    end
    return nothing
end

"""
    generate_layout_key(layout::DataFrame) -> String

Generate a unique key for layout caching based on turbine positions and types.
"""
function generate_layout_key(layout::DataFrame) -> String
    # Create hash of layout for caching
    layout_str = join(["$(row.x)_$(row.y)_$(row.turb)" for row in eachrow(layout)], "|")
    return string(hash(layout_str))
end

end # module SharedComputations