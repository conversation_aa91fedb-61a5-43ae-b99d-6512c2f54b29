"""
Main simulation module for AEP_Julia

Implements the main_optimized function that replicates the Python implementation's
two-stage simulation approach with shared computation optimization.
"""
module MainSimulation

using Printf
using Dates
using Base.Filesystem

# Import all required modules
using ..FlorisInterfaceJulia
using ..SharedComputations
using ..TimeSeries
using ..Layouts
using ..TurbineData
using ..Outputs

export main_optimized, run_floris_optimized

"""
    main_optimized(; inputs_loc=nothing, PlotFlowfield=false, GeneratingTurbEffContourPlots=false)

Main entry point that replicates Python `main_optimized` function.

This function implements the optimized two-stage simulation approach:
1. Run internal turbines simulation
2. Run full simulation (internal + external) or copy results if no external turbines

Parameters:
- inputs_loc: Input directory path (defaults to current directory + "/Input/")
- PlotFlowfield: Whether to generate flow field visualizations
- GeneratingTurbEffContourPlots: Whether to generate turbine efficiency contour plots

Returns:
- Tuple of (internal_gross, internal_waked, full_gross, full_waked) AEP values
"""
function main_optimized(; 
                        inputs_loc=nothing,
                        PlotFlowfield::Bool=false, 
                        GeneratingTurbEffContourPlots::Bool=false)
    
    total_start = time()
    
    # Set default input location
    if inputs_loc === nothing
        work_dir = pwd()
        inputs_loc = joinpath(work_dir, "Input/")
    end
    
    @info "="^60
    @info "STARTING AEP_JULIA OPTIMIZED SIMULATION"
    @info "="^60
    @info "Input location: $inputs_loc"
    
    # Initialize shared computations
    shared = SharedComputations()
    
    # Output locations
    work_dir = dirname(inputs_loc)
    outputs_internal = joinpath(work_dir, "OutputInternal/")
    outputs_full = joinpath(work_dir, "Output/")
    
    # Ensure output directories exist
    mkpath(outputs_internal)
    mkpath(outputs_full)
    
    # Step 1: Load timeseries and compute wind rose ONCE
    @info "Step 1: Loading and processing time series data..."
    shared.ts, shared.WR, shared.wd_array, shared.ws_array = load_and_process_timeseries(inputs_loc)
    
    # Step 2: Prepare turbine YAMLs ONCE
    @info "Step 2: Preparing turbine YAML files..."
    turb_lib_path = joinpath(work_dir, "FLORIS_311_VF1_Operational", "core", "turbine_library")
    if !isdir(turb_lib_path)
        turb_lib_path = joinpath(outputs_internal, "turbine_library")
        mkpath(turb_lib_path)
    end
    
    prepare_turbine_yaml(inputs_loc, turb_lib_path)
    shared.turbine_yamls_prepared = true
    
    # Step 3: Check layouts and external turbines
    @info "Step 3: Loading wind farm layouts..."
    shared.has_external_turbines, shared.layout_internal, shared.layout_full = check_external_turbines(
        "InternalLayout.csv", "layout.csv", inputs_loc
    )
    
    # Step 4: Run internal simulation
    @info "\\n" * "="^60
    @info "STARTING INTERNAL TURBINES SIMULATION"
    @info "="^60
    
    # Create additional info file
    info_file = joinpath(work_dir, "additional-Info.txt")
    open(info_file, "w") do f
        println(f, "Additional Info :")
        println(f, "")
        println(f, "Internal simulation :")
        println(f, "="^80)
        println(f, "")
    end
    
    # Run internal simulation
    internal_gross, internal_waked = run_floris_optimized(
        shared,
        inputs_loc=inputs_loc,
        outputs_loc=outputs_internal,
        layout_file="InternalLayout.csv",
        input_config="config_Jensen_FLS_Validation_FastAEP.yaml",
        parallel=true,
        PlotFlowfield=PlotFlowfield,
        GeneratingTurbEffContourPlots=GeneratingTurbEffContourPlots
    )
    
    # Step 5: Run full simulation or copy results
    @info "\\n" * "="^60
    @info "STARTING FULL SIMULATION (INTERNAL + EXTERNAL)"
    @info "="^60
    
    if !shared.has_external_turbines
        @info "\\n*** NO EXTERNAL TURBINES DETECTED ***"
        @info "Copying internal simulation results to Output directory..."
        
        # Copy all files from OutputInternal to Output
        copied_files = copy_simulation_results(outputs_internal, outputs_full)
        
        @info "Copied $(length(copied_files)) files successfully!"
        if PlotFlowfield
            flow_plots = filter(f -> contains(f, "Flowfield"), copied_files)
            @info "Including $(length(flow_plots)) flow field plots"
        end
        
        full_gross = internal_gross
        full_waked = internal_waked
        
    else
        @info "External turbines detected: $(sum(shared.layout_full.external))"
        @info "Running full simulation with wake effects from external turbines..."
        
        # Run full simulation
        full_gross, full_waked = run_floris_optimized(
            shared,
            inputs_loc=inputs_loc,
            outputs_loc=outputs_full,
            layout_file="layout.csv",
            input_config="config_Jensen_FLS_Validation_FastAEP.yaml",
            parallel=true,
            PlotFlowfield=PlotFlowfield,
            GeneratingTurbEffContourPlots=GeneratingTurbEffContourPlots
        )
    end
    
    # Final summary
    @info "\\n" * "="^60
    @info "SIMULATION SUMMARY"
    @info "="^60
    @info "Internal Turbines - Gross: $(round(internal_gross, digits=2)) GWh, Waked: $(round(internal_waked, digits=2)) GWh"
    @info "Full Farm - Gross: $(round(full_gross, digits=2)) GWh, Waked: $(round(full_waked, digits=2)) GWh"
    
    if shared.has_external_turbines
        external_impact = internal_waked - full_waked
        @info "External turbine wake impact on internal: $(round(external_impact, digits=2)) GWh"
    end
    
    total_elapsed = time() - total_start
    @info "Total runtime: $(round(total_elapsed, digits=2)) seconds"
    @info "="^60
    
    # Append final info to additional-Info.txt
    open(info_file, "a") do f
        println(f, "="^80)
        println(f, "FINAL SUMMARY:")
        println(f, "Internal AEP: $(round(internal_waked, digits=2)) GWh")
        println(f, "Full Farm AEP: $(round(full_waked, digits=2)) GWh")
        println(f, "Total Runtime: $(round(total_elapsed, digits=2)) seconds")
    end
    
    return internal_gross, internal_waked, full_gross, full_waked
end

"""
    run_floris_optimized(shared::SharedComputations; kwargs...)

Optimized FLORIS run function that reuses shared computations.
This function replicates the Python `run_floris_optimized` function.
"""
function run_floris_optimized(shared::SharedComputations;
                             inputs_loc::String,
                             outputs_loc::String,
                             layout_file::String="layout.csv",
                             input_config::String="config_Jensen_FLS_Validation_FastAEP.yaml",
                             logfile_name::String="mylogfile.txt",
                             parallel::Bool=true,
                             PlotFlowfield::Bool=false,
                             GeneratingTurbEffContourPlots::Bool=false,
                             gross_data=nothing,
                             is_reusing_internal::Bool=false)
    
    @info "========================================"
    is_reused = (gross_data !== nothing && "Output/" in outputs_loc) ? "REUSED INTERNAL" : layout_file
    @info "Running $is_reused simulation"
    @info "========================================"
    
    # Setup output directory and turbine library
    setup_output_directory(outputs_loc, inputs_loc)
    
    # Setup log file
    log_loc = joinpath(outputs_loc, logfile_name)
    setup_log_file(log_loc)
    
    start_floris = time()
    
    # Use shared data
    ts = shared.ts
    WR = shared.WR
    wd_array = shared.wd_array
    ws_array = shared.ws_array
    
    # Get appropriate layout
    layout = contains(layout_file, "layout.csv") ? shared.layout_full : shared.layout_internal
    
    # Initialize FLORIS interface
    config_path = joinpath(inputs_loc, input_config)
    fi = FlorisInterfaceJulia(config_path)
    
    # Early return for reused internal simulation
    if is_reusing_internal
        @info "No external turbines detected - copying internal simulation results..."
        return 0.0, 0.0  # Placeholder values
    end
    
    # Compute or use provided gross data
    if gross_data === nothing
        gross_data = compute_gross_power_once(fi, layout, wd_array, ws_array, WR, log_loc=log_loc)
    end
    
    # Extract gross data components
    FL_gross = gross_data["FL_gross"]
    unwaked_final_results = gross_data["unwaked_final_results"]
    unwaked_turb_aeps_df = gross_data["unwaked_turb_aeps_df"]
    gross_AEP = gross_data["gross_AEP"]
    keep_turbs = gross_data["keep_turbs"]
    keep_turb_idxs = gross_data["keep_turb_idxs"]
    ref_ht = gross_data["ref_ht"]
    
    # Save unwaked results
    save_turbine_aep(unwaked_turb_aeps_df.aep, 
                     unwaked_turb_aeps_df.turb_ID,
                     joinpath(outputs_loc, "Summarized_turbine_AEP_NOWAKE.csv"))
    
    @info "=================================================================="
    @info "Baseline AEP: $(round(gross_AEP, digits=3)) GWh"
    @info "=================================================================="
    
    # Calculate sensitivity factor (only for full simulation)
    if contains(outputs_loc, "Output/") && shared.sensitivity_factor === nothing
        shared.sensitivity_factor = calculate_sensitivity_factor(
            ts, FL_gross, wd_array, ws_array, layout, keep_turbs, keep_turb_idxs
        )
    end
    
    write_log("Unwaked/gross AEP calculation complete!", log_loc)
    
    # Plot flow fields if requested
    if PlotFlowfield && !is_reusing_internal
        generate_flow_field_plots(fi, outputs_loc, ref_ht, log_loc)
    end
    
    # Calculate waked power
    start_waked = time()
    @info "Now calculating wakes"
    
    # Reinitialize for wake calculation
    reinitialize(fi,
                layout=(layout.x, layout.y),
                turbine_type=layout.turb,
                wind_directions=wd_array,
                wind_speeds=ws_array)
    
    before_mem = memory_footprint()
    
    if parallel
        n_cores = min(6, Threads.nthreads())
        write_log("Beginning Waked AEP calculation (parallel mode, cores: $n_cores)", log_loc)
        
        # Check if we need to calculate external turbines
        calculate_external = contains(outputs_loc, "Output/")
        
        FL_net = calculate_wake_parallel(fi, n_cores, log_loc, calculate_external)
        
        elapsed_wake = time() - start_waked
        @info "Calculate wake parallel using $n_cores cores: $(round(elapsed_wake, digits=2)) seconds"
    else
        write_log("Beginning Waked AEP calculation (serial mode)", log_loc)
        calculate_wake(fi, logfile=log_loc)
        FL_net = get_turbine_powers(fi) ./ 1E6  # Convert to MW
        elapsed_wake = time() - start_waked
        @info "Calculate wake serial: $(round(elapsed_wake, digits=2)) seconds"
    end
    
    # Ensure net power doesn't exceed gross
    mask = FL_net .> FL_gross
    FL_net[mask] = FL_gross[mask]
    
    after_mem = memory_footprint()
    @info "Memory (in MB) being used by Julia process = $(after_mem - before_mem)"
    
    # Process waked results
    waked_results = process_waked_results(WR, FL_net, wd_array, ws_array, layout, keep_turbs, keep_turb_idxs)
    
    write_log("Wake calculation total runtime: $(round(time() - start_waked, digits=2)) sec", log_loc)
    
    # Calculate waked AEP
    waked_aep = sum(waked_results["turbine_aeps"])
    
    # Save waked results
    save_turbine_aep(waked_results["turbine_aeps"],
                     waked_results["turbine_ids"],
                     joinpath(outputs_loc, "Summarized_turbine_AEP.csv"))
    
    # Create production time series (only for full simulation)
    if contains(outputs_loc, "Output/")
        save_production_timeseries(ts, layout, FL_net, FL_gross, wd_array, ws_array,
                                  joinpath(outputs_loc, "production_timeseries.csv"))
    end
    
    @info "=================================================================="
    @info "Waked AEP: $(round(waked_aep, digits=3)) GWh"
    @info "=================================================================="
    
    # Generate summary outputs
    generate_summary_outputs(waked_results["final_results"], unwaked_final_results,
                            layout, keep_turb_idxs, outputs_loc, "navarro")
    
    elapsed_total = time() - start_floris
    write_log("Simulation complete! time: $(round(elapsed_total, digits=2)) sec", log_loc)
    @info "Simulation complete! Runtime: $(round(elapsed_total, digits=2)) sec"
    
    return gross_AEP, waked_aep
end

"""
Supporting functions for main simulation
"""

function setup_output_directory(outputs_loc::String, inputs_loc::String)
    # Create output directory if it doesn't exist
    mkpath(outputs_loc)
    
    # Copy turbine library to output directory
    turb_lib_src = joinpath(dirname(inputs_loc), "FLORIS_311_VF1_Operational", "core", "turbine_library")
    turb_lib_dst = joinpath(outputs_loc, "turbine_library")
    
    if isdir(turb_lib_dst)
        rm(turb_lib_dst, recursive=true)
    end
    
    if isdir(turb_lib_src)
        cp(turb_lib_src, turb_lib_dst)
    else
        # Create minimal turbine library directory
        mkpath(turb_lib_dst)
    end
end

function setup_log_file(log_loc::String)
    # Truncate existing log file
    open(log_loc, "w") do f
        println(f, "FLORIS Julia simulation log - $(Dates.now())")
        println(f, "="^50)
    end
end

function copy_simulation_results(src_dir::String, dst_dir::String)
    copied_files = String[]
    
    for file in readdir(src_dir)
        src_file = joinpath(src_dir, file)
        if isfile(src_file)
            dst_file = joinpath(dst_dir, file)
            cp(src_file, dst_file, force=true)
            push!(copied_files, file)
        end
    end
    
    return copied_files
end

function generate_flow_field_plots(fi, outputs_loc::String, ref_ht::Float64, log_loc::String)
    @info "Generating flow field plots..."
    start_time = time()
    
    wind_directions = collect(0:30:330)  # Every 30 degrees
    write_log("Calculating flowfield visualization for: $(length(wind_directions)) directions", log_loc)
    
    # For now, just log that we would generate plots
    # Actual implementation would call plot generation functions
    for wd in wind_directions
        @info "Would generate flow field plot for $(wd)°"
    end
    
    elapsed = time() - start_time
    @info "Generated flow field plots: $(round(elapsed, digits=2)) sec"
    write_log("Flow field plotting complete: $(round(elapsed, digits=2)) sec", log_loc)
end

function calculate_wake_parallel(fi, n_cores::Int, log_loc::String, calculate_external::Bool)
    # Placeholder for parallel wake calculation
    # This would implement the parallel processing logic
    @info "Parallel wake calculation with $n_cores cores (calculate_external: $calculate_external)"
    
    # For now, call the serial version
    calculate_wake(fi, logfile=log_loc)
    return get_turbine_powers(fi) ./ 1E6  # Convert to MW
end

function process_waked_results(WR::DataFrame, FL_net::Array, wd_array::Vector, ws_array::Vector,
                              layout::DataFrame, keep_turbs::Vector, keep_turb_idxs::Vector)
    # Process waked power results into the expected format
    ordered_powers = []
    
    for row in eachrow(WR)
        ws = row.ws
        wd = row.wd
        wd_idx = argmin(abs.(wd_array .- wd))
        ws_idx = argmin(abs.(ws_array .- ws))
        powers = FL_net[wd_idx, ws_idx, :]
        push!(ordered_powers, powers)
    end
    
    # Convert to DataFrame
    power_matrix = hcat(ordered_powers...)
    colnames = ["$(id)_MW" for id in layout.turb_ID]
    turbine_powers_df = DataFrame(power_matrix', colnames)
    
    # Fill NaN values with zeros and select internal turbines
    for col in names(turbine_powers_df)
        turbine_powers_df[!, col] = coalesce.(turbine_powers_df[!, col], 0.0)
    end
    turbine_powers_df = turbine_powers_df[:, keep_turbs]
    
    # Create final results DataFrame
    waked_final_results = hcat(WR, turbine_powers_df)
    waked_final_results[!, "farm_power [MW]"] = sum(Matrix(waked_final_results[:, keep_turbs]), dims=2)
    
    # Calculate individual turbine AEPs
    turbine_aeps = Float64[]
    for turb_col in keep_turbs
        aep = sum(waked_final_results.freq .* waked_final_results[:, turb_col]) * 8766 / 1E3  # GWh
        push!(turbine_aeps, aep)
    end
    
    return Dict(
        "final_results" => waked_final_results,
        "turbine_aeps" => turbine_aeps,
        "turbine_ids" => layout.turb_ID[keep_turb_idxs]
    )
end

end # module MainSimulation