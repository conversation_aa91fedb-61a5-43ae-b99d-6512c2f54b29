"""
Simulation module - aggregates all simulation functionality
"""
module Simulation

include("Simulation/floris_interface.jl")
include("Simulation/shared_computations.jl")
include("Simulation/main_simulation.jl")

using .FlorisInterfaceJulia
using .SharedComputations
using .MainSimulation

# Re-export key types and functions
export FlorisInterfaceJulia, reinitialize, calculate_wake, calculate_no_wake, get_turbine_powers
export SharedComputations, compute_gross_power_once, check_external_turbines
export main_optimized, run_floris_optimized

end # module Simulation