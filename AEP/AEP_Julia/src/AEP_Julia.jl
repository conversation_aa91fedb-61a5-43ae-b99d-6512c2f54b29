"""
AEP_Julia - Julia Migration of Python FLORIS AEP Calculation

This module provides a drop-in replacement for the Python FLORIS AEP calculation
system, maintaining identical input/output formats while leveraging <PERSON>'s
performance advantages.

Key Features:
- Compatible with existing Python input files (CSV layouts, timeseries, turbine data)
- Implements custom Navarro wake model with exact Python equivalency
- Two-stage simulation approach (Internal + External turbines)
- Parallel processing with shared computation optimization
- Identical output formats to Python implementation

Usage:
```julia
using AEP_Julia

# Run main simulation (equivalent to python new2-original.py)
main_optimized(
    inputs_loc = "/path/to/Input/",
    outputs_loc = "/path/to/Output/",
    PlotFlowfield = true,
    GeneratingTurbEffContourPlots = false
)
```
"""
module AEP_Julia

using CSV
using DataFrames
using Dates
using Distributed
using Interpolations
using LinearAlgebra
using Plots
using Printf
using Statistics
using YAML

# Import FLORIS.jl components from the main repository
include("../../../../floris-js/floris-julia/src/Floris.jl")
using .Floris

# Export main functions for compatibility with Python interface
export main_optimized, run_floris_optimized, load_and_process_timeseries
export prepare_turbine_yaml, compute_gross_power_once
export SharedComputations

# Include submodules
include("InputOutput/layouts.jl")
include("InputOutput/timeseries.jl")
include("InputOutput/turbine_data.jl")
include("InputOutput/outputs.jl")

include("WakeModels/navarro.jl")

include("Simulation/floris_interface.jl")
include("Simulation/shared_computations.jl")
include("Simulation/main_simulation.jl")

include("Utilities/data_processing.jl")
include("Utilities/plotting.jl")
include("Utilities/parallel_processing.jl")

# Re-export key functionality
using .InputOutput
using .WakeModels
using .Simulation
using .Utilities

export load_layout, load_timeseries, save_results
export NavarroWakeModel
export FlorisInterfaceJulia, run_simulation
export process_wind_rose, parallel_wake_calculation

end # module AEP_Julia