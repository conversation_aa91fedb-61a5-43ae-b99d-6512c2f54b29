"""
Utilities module - aggregates utility functions
"""
module Utilities

using DataFrames
using Statistics
using Printf

export custom_round, memory_footprint, write_log

"""
    custom_round(value::Float64, base::Int) -> Float64

Round value to nearest multiple of base (matching Python behavior).
"""
function custom_round(value::Float64, base::Int)
    return Float64(round(Int, value / base) * base)
end

"""
    memory_footprint() -> Float64

Get current memory usage in MB (approximation).
Julia doesn't have direct equivalent to Python's memory_profiler.
"""
function memory_footprint()
    # Placeholder - returns 0 for now
    return 0.0
end

"""
    write_log(message::String, log_file::String)

Write message to log file with timestamp.
"""
function write_log(message::String, log_file::String)
    timestamp = Dates.now()
    open(log_file, "a") do f
        println(f, "[$timestamp] $message")
    end
end

end # module Utilities